/**
 * Utility to test and verify retry configuration detection
 * This helps ensure the SauceLabs reporting gets the correct retry count
 */

interface BrowserConfig {
  cucumberOpts?: {
    retry?: number;
  };
}

interface GlobalWithBrowser {
  browser?: {
    config?: BrowserConfig;
  };
}

export class RetryConfigTester {
  /**
   * Get the actual retry configuration from the current config
   * This mirrors the logic in wdio.shared.conf.ts
   * Priority: CLI --retry > cucumberOpts.retry > default (3)
   */
  static getMaxRetries(): number {
    // Check CLI arguments first (highest priority)
    const cliRetryArg = process.argv.find(arg => arg.startsWith('--retry'));
    if (cliRetryArg) {
      const retryValue = cliRetryArg.split('=')[1] || process.argv[process.argv.indexOf(cliRetryArg) + 1];
      if (retryValue && !isNaN(Number(retryValue))) {
        const totalAttempts = Number(retryValue) + 1; // CLI retry is additional attempts, so +1 for total attempts
        console.log(`🔧 Using CLI retry: --retry ${retryValue} = ${totalAttempts} total attempts`);
        return totalAttempts;
      }
    }

    // Check cucumber options retry setting (fallback)
    const cucumberRetry = (global as GlobalWithBrowser).browser?.config?.cucumberOpts?.retry;
    if (cucumberRetry !== undefined && !isNaN(Number(cucumberRetry))) {
      const totalAttempts = Number(cucumberRetry) + 1; // Cucumber retry is additional attempts, so +1 for total attempts
      console.log(`🔧 Using cucumberOpts retry: ${cucumberRetry} = ${totalAttempts} total attempts`);
      return totalAttempts;
    }

    // Default to 3 total attempts (1 original + 2 retries)
    console.log('🔧 Using default retry: 3 total attempts (1 original + 2 retries)');
    return 3;
  }

  /**
   * Log current retry configuration for debugging
   */
  static logRetryConfig(): void {
    console.log('🔧 Retry Configuration Detection:');
    console.log(`   CLI Args: ${process.argv.join(' ')}`);

    const cliRetryArg = process.argv.find(arg => arg.startsWith('--retry'));
    const cucumberRetry = (global as GlobalWithBrowser).browser?.config?.cucumberOpts?.retry;

    if (cliRetryArg) {
      const retryValue = cliRetryArg.split('=')[1] || process.argv[process.argv.indexOf(cliRetryArg) + 1];
      console.log(`   ✅ CLI Retry (Priority 1): ${cliRetryArg} = ${retryValue} retries`);
    } else {
      console.log('   ❌ CLI Retry: Not provided');
    }

    if (cucumberRetry !== undefined) {
      console.log(`   ✅ CucumberOpts Retry (Priority 2): ${cucumberRetry} retries`);
    } else {
      console.log('   ❌ CucumberOpts Retry: Not configured');
    }

    const maxRetries = this.getMaxRetries();
    console.log(`   🎯 Final Configuration: ${maxRetries} total attempts`);
  }

  /**
   * Validate that the retry configuration makes sense
   */
  static validateRetryConfig(): boolean {
    const maxRetries = this.getMaxRetries();
    
    if (maxRetries < 1) {
      console.error('❌ Invalid retry configuration: maxRetries must be at least 1');
      return false;
    }
    
    if (maxRetries > 10) {
      console.warn('⚠️  High retry count detected: maxRetries = ' + maxRetries);
    }
    
    console.log('✅ Retry configuration is valid: maxRetries = ' + maxRetries);
    return true;
  }
}
