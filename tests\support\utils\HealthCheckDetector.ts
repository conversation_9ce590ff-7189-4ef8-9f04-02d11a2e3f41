/**
 * Utility for detecting health check URLs and service initialization scenarios
 * Used to filter out non-test scenarios from SauceLabs reporting
 */

/**
 * Helper function to detect health check URLs and service initialization
 */
export function isHealthCheckOrServiceUrl(url?: string): boolean {
  if (!url) return false;

  const healthCheckPatterns = [
    /127\.0\.0\.1:\d+\/health/,
    /localhost:\d+\/health/,
    /\/health$/,
    /\/status$/,
    /\/ping$/,
    /sauce-connect/,
    /saucelabs\.com.*\/health/,
    /appium.*\/health/,
    /webdriver.*\/health/,
  ];

  return healthCheckPatterns.some(pattern => pattern.test(url));
}

/**
 * Helper function to detect if this is a SauceLabs service initialization scenario
 */
export function isServiceInitializationScenario(scenarioName?: string, featureName?: string): boolean {
  if (!scenarioName && !featureName) return true; // No scenario/feature info = likely service init

  // Check for empty or generic scenario names that indicate service initialization
  if (!scenarioName || scenarioName.trim() === '' || scenarioName === 'undefined') {
    return true;
  }

  // Check for feature file names that indicate health checks or service initialization
  if (featureName && (
    featureName.toLowerCase().includes('health') ||
    featureName.toLowerCase().includes('service') ||
    featureName.toLowerCase().includes('init') ||
    featureName === 'undefined' ||
    featureName.trim() === ''
  )) {
    return true;
  }

  const servicePatterns = [
    /health.*check/i,
    /service.*init/i,
    /sauce.*connect/i,
    /appium.*init/i,
    /webdriver.*init/i,
    /session.*init/i,
    /^test$/i, // Generic "test" scenarios
    /^scenario$/i, // Generic "scenario" scenarios
    /^feature$/i, // Generic "feature" scenarios
  ];

  const nameToCheck = `${scenarioName || ''} ${featureName || ''}`.toLowerCase();
  return servicePatterns.some(pattern => pattern.test(nameToCheck));
}

/**
 * Helper function to check if session is valid
 */
export async function isSessionValid(): Promise<boolean> {
  try {
    await browser.getTitle();
    return true;
  } catch (error) {
    console.log('Session validation failed:', error instanceof Error ? error.message : String(error));
    return false;
  }
}
